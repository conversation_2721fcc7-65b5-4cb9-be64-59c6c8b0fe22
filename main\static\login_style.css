html,body{
    margin: 0;
}
:root{
    --font:"Poppins", sans-serif;
    --primary:
}
.container{
    width:400px;
    height:fit-content;
    margin: auto;
    background-color: transparent;
    scale: 0.9;
}
@media screen and (max-width:400px) {
    body{
        overflow: hidden;
        margin: 20px;
        min-width: 400px;
    }
}
.top_bar{
    width:100%;
    display: flex;
    flex-direction: row;
    
}
.back_button{
    background-color: transparent;
    color: blueviolet;
    border: 0;
    outline: 0;
    font-size: 50px;
    width: 10px;
    margin-left: -15px;
}
.logo{
    width:70px;
    margin-left: auto;
}
h2{
    font-family: var(--font);
    font-size: 30px;
    font-weight: 600;
    margin-bottom: 10px;
}
p{
    font-family: var(--font);
    font-size: 12px;
    font-weight: 700;
    margin-bottom: 10px;
    color: rgb(94, 94, 94);
    text-transform: capitalize;
}
.signin-form > span{
    font-family: var(--font);
    display: flex;
    flex-direction: column;
    font-size: 20px;
    font-weight: 600;
    margin-top: 30px;
    color: var(--primary);
}
.signin-form > span > input{
    border: 0;
    outline: 0;
    font-size: 18px;
    padding:10px;
    margin-top: 10px;
    border-bottom: 3px solid blueviolet;
}
#pass::-ms-clear,#password::-ms-clear,#confirm::-ms-clear{
    display: none;
}
.signin-form > span > a{
    font-size: 15px;
    margin-left: auto;
    text-decoration: none;
    color:blueviolet;
    font-weight: 700;
    margin-top: 15px;
    letter-spacing: 0px;
}
.signin-form > p{
    font-size: 15px;
    color: rgb(39, 39, 39);
    text-align: center;
    margin-top: 20px;
    padding-bottom: 40px;
}
.signin-form > p > a{
    text-decoration: none;
    color:blueviolet;
   
}
#see{
    width: 35px;
    height: 25px;
    margin-left: calc(100% - 35px);
    margin-top: -35px;
    background-color: transparent;
    outline: 0;
    border: 0;
}
.signin-button{
    width: 100%;
    border: 0;
    outline: 0;
    color: rgb(255, 255, 255);
    height: 50px;
    font-family: var(--font);
    font-size: 25px;
    text-align: center;
    background-color: blueviolet;
    font-weight: 600;
    margin-top: 15px;
    border-radius: 20px;
}
#err{
    color: red;
    text-transform: uppercase;
    font-size: 12px;
    font-weight: 900;
    background-color: #e817003a;
    padding: 10px;
    border-left: 5px solid blueviolet;
    border-radius: 5px;
}
# Generated by Django 5.0.2 on 2024-02-16 04:34

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='User',
            fields=[
                ('uid', models.AutoField(primary_key=True, serialize=False)),
                ('name', models.<PERSON>r<PERSON><PERSON>(max_length=100)),
                ('email', models.Email<PERSON>ield(max_length=254, unique=True)),
                ('password', models.Char<PERSON>ield(max_length=128)),
                ('email_verified', models.<PERSON><PERSON>an<PERSON>ield(default=False)),
                ('additional_params', models.TextField(blank=True, null=True)),
            ],
        ),
    ]

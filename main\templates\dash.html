<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="/static/dash.css">
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:ital,wght@0,100..900;1,100..900&display=swap" rel="stylesheet">
	<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <title>Dashboard</title>
</head>
<body>
    
        <div class="profile-container">
            <div style="background-image: url('/static/{{profile}}');" class="profile-pic" id="profileI"></div>
            <div class="logo">
                <img src="/static/images/Logo.png" alt="Center Image">
                <p >Planet</p>
            </div>
            <img src="/static/images/bell.png" alt="Bell Icon">
        </div>

        <div class="homepage" id="homepage" style="display: block;">
            <h3>Hello,<b>{{user}}</b></h3>
            <div class="posts" id="posts">
            
            </div>
            <div class="gap"></div>
        </div>


    <div class="profilepage" id="profilepage" style="display: none;">
        <div class="box">
            <img src="/static/{{profile}}" id="profileI2">
            <h3>{{user}}</h3>
            <form id="uploadForm2" enctype="multipart/form-data">
                {% csrf_token %}
            <label for="imageInput1" class="upload">Change Profile</label>
            <input type="file" name="image"  id="imageInput1" style="visibility: hidden;">
            </form>
            <button onclick="window.location.href='/logout'">Logout</button>
        </div>
    </div>


      <footer>
        <button class="home" id="home" onclick="tabs('home','profile','homepage','profilepage','block')"><img src="/static/images/Home.png"></button>

        <form id="uploadForm" enctype="multipart/form-data">
            {% csrf_token %}
            <label for="imageInput2" class="add"> <img src="/static/images/Upload.png" alt=""></label>
            <input type="file" name="image"  id="imageInput2" style="visibility: hidden;">
        </form>
        <button class="profile" id="profile" onclick="tabs('profile','home','profilepage','homepage','flex')"><img src="/static/images/prof.png" alt=""></button>
      </footer>
	<script>
		$(document).ready(function() {
    $('#uploadForm').change(function(event) {
		console.log("image");
        event.preventDefault();
        var formData = new FormData($(this)[0]);

        $.ajax({
            url: '/upload',  
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            xhr: function() {
                var xhr = new window.XMLHttpRequest();
                xhr.upload.addEventListener('progress', function(evt) {
                    if (evt.lengthComputable) {
                        var percentComplete = (evt.loaded / evt.total) * 100;
                        $('#progressBar').val(percentComplete);
                        $('#progressPercent').text(percentComplete.toFixed(2) + '%');
                    }
                }, false);
                return xhr;
            },
            beforeSend: function() {
                $('#uploadButton').prop('disabled', true);
                $('#progressWrapper').show();
            },
            success: function(data) {
                alert('Upload successful!');
            },
            error: function(xhr, status, error) {
                alert('Upload failed: ' + error);
            },
            complete: function() {
                $('#uploadButton').prop('disabled', false);
                $('#progressWrapper').hide();
            }
        });
    });
    $('#uploadForm2').change(function(event) {
		console.log("profile");
        event.preventDefault();
        var formData = new FormData($(this)[0]);

        $.ajax({
            url: '/profile_upload',  
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            xhr: function() {
                var xhr = new window.XMLHttpRequest();
                xhr.upload.addEventListener('progress', function(evt) {
                    if (evt.lengthComputable) {
                        var percentComplete = (evt.loaded / evt.total) * 100;
                        $('#progressBar').val(percentComplete);
                        $('#progressPercent').text(percentComplete.toFixed(2) + '%');
                    }
                }, false);
                return xhr;
            },
            beforeSend: function() {
                $('#uploadButton').prop('disabled', true);
                $('#progressWrapper').show();
            },
            success: function(data) {
                location.reload();


                alert('Upload successful!');
            },
            error: function(xhr, status, error) {
                alert('Upload failed: ' + error);
            },
            complete: function() {
                $('#uploadButton').prop('disabled', false);
                $('#progressWrapper').hide();
            }
        });
    });
});

function heart(a){
    console.log(a);
    if(a.style.padding=="1px"){
        a.style.padding="0px";
        document.getElementById("circle").style.animation="heart 1s normal"
        console.log("df");
        setTimeout(()=>{
            a.style.backgroundImage="url(/static/images/heart_f.png)";
        },800)
        setTimeout(()=>{
            document.getElementById("circle").style.animation="none"
        },1000)
    }
    else{
        a.style.padding="1px";
        document.getElementById("circle").style.animation="heart 1s reverse"
        console.log("dh");
        setTimeout(()=>{
            a.style.backgroundImage="url(/static/images/heart_h.png)";
        },500)
        setTimeout(()=>{
            document.getElementById("circle").style.animation="none"
        },1000)
    }
}
function tabs(a,b,c,d,e){
    document.getElementById(a).style.backgroundColor="#e1d6ff";
    document.getElementById(a).style.boxShadow="2px 2px 5px rgb(131, 131, 131) inset";
    document.getElementById(b).style.backgroundColor="white";
    document.getElementById(b).style.boxShadow="0px 0px";
    document.getElementById(c).style.display=e;
    document.getElementById(d).style.display="none"
}
posts()
function posts() {
    fetch("https://c646-2409-4073-2094-7ccc-ac10-f85d-c925-c65d.ngrok-free.app/uploads")
        .then((res) => res.json())
        .then((json) => {
            json = json.reverse();
            arr1 = json;

            console.log(json[0].name);
            console.log(json[0].filename);
            console.log(json[0].name);
            console.log(json[0].profile);

            // Clear existing posts
            document.getElementById("posts").innerHTML = '';

            for (let i = 0; i <= json.length - 1; i++) {
                var tags = "#" + json[i].tags[0] + " ";
                for (let j = 1; j <= json[i].tags.length - 1; j++) {
                    tags += " " + json[i].tags[j] + " ";
                }
                tags[0] = "#";
                document.getElementById("posts").innerHTML += "<div class='post'><img src=/static/" + json[i].filename + "/><div class='post_options'><div class='porf' style='background-image:url(/static/" + json[i].profile + ")'></div><h2>" + json[i].name + "</h2><div class='heart' style='background-image: url(/static/images/heart_h.png);padding:1px' onclick='heart(this)'><div class='circle' id='circle'></div></div></div><p>This is demo description of the post</p><b>" + tags + "</b></div>"
            }

        })
        .catch((error) => {
            console.error('Error:', error);
        });
}

var arr1;

setInterval(() => {
    fetch("https://c646-2409-4073-2094-7ccc-ac10-f85d-c925-c65d.ngrok-free.app/uploads")
        .then((res) => res.json())
        .then((json) => {
            var newArr = json;

            // Compare arrays
            if (JSON.stringify(arr1) !== JSON.stringify(newArr)) {
                arr1 = newArr;
                posts();
            } else {
                console.log("No changes detected");
            }
        })
        .catch((error) => {
            console.error('Error:', error);
        });
}, 1000);

</script>

    
</body>

</html>

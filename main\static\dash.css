html,body{
  margin: 0px;
  font-family: "Montserrat", sans-serif;
  background-color:#EEE8FF;
}
.profile-container{
  width:100vw;
  height: 60px;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  position: fixed;
 background-color:#EEE8FF;
 padding: 10px 0px;
}
.profile-container > img{
  width:35px;
  height: fit-content;
  margin: auto 20px;
}
.profile-pic{
  width: 60px;
  height: 60px;
  border-radius: 100px;
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
  border: 2px solid blueviolet;
  margin: auto 20px;
}
.logo{
  margin: auto;
  display: flex;
}
.logo > img{
  width: 50px;
  height: fit-content;
}
.logo > p{
  font-size: 28px;
  margin: auto 10px;
  font-weight: 500;
  color: blueviolet;
}
.homepage{
  width: 400px;
  height: 100svh;
  margin: auto;

}
.homepage > h3{
  display: flex;
  flex-direction: column;
  font-size: 25px;
  font-weight: 500;
  padding:0px;
  padding-top: 120px;
  margin: 0;
  padding-bottom: 30px;

}
.homepage > h3 > b{
  padding:10px 0px;
  color: blueviolet;
}
.post{
  width: 100%;
  height: fit-content;
  border-radius: 20px;
  box-shadow: 2px 2px 5px rgb(161, 161, 161);
  display: flex;
  flex-direction: column;
  margin-bottom: 30px;
  background-color: white;
}
.post > img{
  width: 100%;
  height: auto;
  border-radius: 20px 20px 0px 0px;
}
.post > b{
  color: blueviolet;
  padding: 10px;
}
.post > p{
  margin: 10px ;
  margin-top: 30px;
}
.post_options{
  width: calc(100% - 30px);
  height: 50px;
  border-radius: 100px;
  margin: auto;
  margin-top: -80px;
  padding: 5px;
  background-color: rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  display: flex;
  flex-direction: row;
}
.gap{
  width: 100%;
  height: 70px;
}
.heart{
  width: 25px;
  height: 25px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  margin: auto 10px;
  margin-left: auto;
  position: relative;

}
.circle{
  border-radius: 400px;
  background-color: #b81b47;
  /* animation: heart 1s normal; */
  position: absolute;
}
@keyframes heart {
  0%{
    width: 0px;
    height: 0px;
    opacity: 1;
    top: 50%;
  left: 50%;
  }
  80%{
    width: 25px;
    height: 25px;
    opacity: 1;
    top: 0%;
  left: 0%;
  }
  100%{
    width: 25px;
    height: 25px;
    opacity: 0;
    top: -50%;
  left: 0%;
  }
}
 .porf{
  width: 50px;
  height: 50px;
  border-radius: 100px;
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
}
.post_options > h2{
  color: white;
  font-size: 18px;
  margin: auto 10px;
  font-weight: 500;
}

footer{
  width: 100%;
  height: 50px;
  position: fixed;
  bottom: 0;
  background-color: rgb(255, 255, 255);
  display: flex;
  flex-direction: row;
  justify-content: space-around;
}
footer > button{
  width: 60px;
  height: 30px;
  margin: auto;
  padding: 0;
  outline: 0;
  border: 0;
  border-radius: 100px;
}
.home{
  background-color:#e1d6ff ;
  box-shadow: 2px 2px 5px rgb(131, 131, 131) inset;
}
.add{
  display: block;
  scale: 2.5;
  transform: translateY(-10px);
  background-color: transparent;
  box-shadow: 0px 0px;
  width: 60px;
  height: 30px;
  margin: auto;
  border-radius: 100px;
  text-align: center;
}
.add > img{
  width: 30px;
  height: fit-content;
}
.profile{
  background-color: white;
  box-shadow: 0px 0px

}
footer > button > img{
  width: 30px;
  height: fit-content;
}
.profilepage{
  width: 400px;
  height: 100svh;
  margin: auto;
  display: flex;
}
.box{
  width: calc(100% - 30px);
  height: fit-content;
  box-sizing: border-box;
  padding: 15px;
  border-radius: 20px;
  background-color: white;
  margin: auto;
  display: flex;
  flex-direction: column;
}
.box > img{
  width: 150px;
  height: fit-content;
  clip-path: circle(40%);
  margin: auto;
  margin-top: -100px;
}
.box > h3{
  margin: auto;
}
.box > button{
  width: 100%;
  height: 50px;
  border: 0;
  outline: 0;
  border-radius: 10px;
  color: white;
  background-color: blueviolet;
  font-size: 18px;
}
.upload{
  display: block;
  width: 100%;
  height: 50px;
  border-radius: 10px;
  color: white;
  background-color: blueviolet;
  font-size: 18px;
  text-align: center;
  line-height: 50px;
  margin-top: 30px;
}









@media screen  and (max-width:430px){
  .homepage{
    width:calc(100vw - 30px)
  }
  .profilepage{
    width:calc(100vw - 30px)
  }
  .profile-container > img{
    width: 30px;
  }
  .logo > img{
    width: 40px;
    height: fit-content;
  }
  .logo > p{
    font-size: 18px;
    margin: auto 10px;
    font-weight: 500;
    color: blueviolet;
  }
  .profile-pic{
    width: 40px;
    height: 40px;
}
.homepage > h3{
  font-size: 22px;
}
}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=0.9, user-scalable=no">
    <title>Email Verification Completed</title>
    <link rel="stylesheet" href="/static/unverfied.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
    
    <style>
        body {
            font-family: "Poppins", sans-serif;
            background-color: #f5f5f5;
            text-align: center;
        }
        .message {
            font-family: "Poppins", sans-serif;
            font-size: 20px;
            margin-bottom: 20px;
        }
        .redirect-info {
            font-family: "Poppins", sans-serif;
            color: #666;
            margin-bottom: 20px;
        }
        a{
            text-decoration: none;
            color: #e81900;
            font-weight: bold;

        }
    </style>
</head>
<body>
    <div class="container">
        <i class="fa fa-check" style="background-color: green;line-height: 80px;"></i>
        <div class="message">Verification completed.</div>
        <div class="redirect-info">Redirecting to <a href="/dash">Dashboard</a> in <span id="countdown">3</span> seconds.</div>
    </div>
    
    

    <script>
        function redirect() {
            setTimeout(function() {
                window.location.href = "/dash";
            }, 3000);  
        }

        window.onload = function() {
            redirect();
            
            var countdown = 3;
            setInterval(function() {
                countdown = countdown - 1;
                document.getElementById('countdown').innerHTML = countdown;
            }, 1000);
        };
    </script>
</body>
</html>
